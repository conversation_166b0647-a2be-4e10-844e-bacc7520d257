#!/usr/bin/env python3
"""
测试app.py中实时TTS功能的简化版本
"""

import asyncio
import json
from typing import List

class MockChatEvent:
    def __init__(self, content="", id="", type=""):
        self.content = content
        self.id = id
        self.type = type

class MockAdditionalOutputs:
    def __init__(self, event):
        self.event = event

class MockLLMChat:
    """模拟LLM聊天客户端"""
    
    async def stream_chat_with_interrupt(self, prompt: str, model: str = ""):
        """模拟流式聊天响应"""
        # 模拟LLM逐步生成的响应
        response_parts = [
            "你好！", "我正在", "为您", "搜索", "相关", "商品", "。",
            "请稍等", "片刻", "，", "我会", "为您", "找到", "最合适", "的", "选择", "。",
            "\n```json\n",
            '{\n',
            '    "internal": "执行商品搜索",\n',
            '    "call": {"name": "search_products", "query": "手机"},\n',
            '    "intent": "search_execute"\n',
            '}\n',
            "```"
        ]
        
        for part in response_parts:
            await asyncio.sleep(0.1)  # 模拟生成延迟
            yield part

class RealTimeTTSHandler:
    """实时TTS处理器"""
    
    def __init__(self):
        self.voice_queue = asyncio.Queue()
        self.voice_inqueue = asyncio.Queue()
        self.chat = MockLLMChat()
    
    async def extract_complete_sentences(self, text_buffer: str) -> List[str]:
        """从文本缓冲区中提取完整的句子"""
        try:
            # 中文句子结束标点符号
            sentence_endings = ['。', '！', '？', '；', '.', '!', '?', ';', '…', '...']
            
            sentences = []
            current_sentence = ""
            
            i = 0
            while i < len(text_buffer):
                char = text_buffer[i]
                current_sentence += char
                
                # 检查是否是句子结束符
                if char in sentence_endings:
                    # 检查是否是省略号的一部分
                    if char == '.' and i + 2 < len(text_buffer) and text_buffer[i:i+3] == '...':
                        current_sentence += text_buffer[i+1:i+3]
                        i += 2
                    
                    # 添加完整句子
                    if current_sentence.strip():
                        sentences.append(current_sentence.strip())
                        current_sentence = ""
                
                i += 1
            
            # 如果缓冲区中有足够长的未完成句子（超过8个字符），也发送出去
            if len(current_sentence.strip()) > 8:
                # 寻找合适的断点（逗号、顿号等）
                break_points = ['，', '、', ',', ' ']
                best_break = -1
                
                for j in range(len(current_sentence) - 1, max(0, len(current_sentence) - 5), -1):
                    if current_sentence[j] in break_points:
                        best_break = j + 1
                        break
                
                if best_break > 0:
                    sentences.append(current_sentence[:best_break].strip())
            
            return sentences
            
        except Exception as e:
            print(f"❌ 提取句子出错: {e}")
            return []

    async def send_real_time_tts(self, text: str, conv_id: str):
        """发送实时TTS - 模拟版本"""
        try:
            if not text or not text.strip():
                return
                
            text = text.strip()
            print(f"🎵 实时TTS发送: '{text}'")

            # 模拟发送到前端显示
            display_event = MockChatEvent()
            display_event.content = text
            display_event.id = conv_id + "_tts_response"
            display_event.type = 'msg'
            await self.voice_queue.put(MockAdditionalOutputs(display_event))

            # 模拟发送到TTS处理器
            tts_event = MockChatEvent()
            tts_event.content = text
            tts_event.id = conv_id + "_realtime_tts"
            tts_event.type = 'tts'
            await self.voice_inqueue.put(tts_event)
            
            print(f"✅ 实时TTS已发送到处理器: '{text}'")
            
            # 模拟TTS播放时间
            await asyncio.sleep(len(text) * 0.1)  # 根据文本长度模拟播放时间

        except Exception as e:
            print(f"❌ 发送实时TTS出错: {e}")

    async def stream_process_with_immediate_tts(self, prompt: str, transcript: str, conv_id: str):
        """流式处理LLM响应，实时播放TTS内容 - 测试版本"""
        try:
            print(f"🎵 开始真正的流式TTS处理")

            # 状态变量
            tts_content = ""
            json_started = False
            tts_streaming_active = False
            tts_content_finalized = False
            sentence_buffer = ""   # 句子缓冲区

            # 流式获取LLM响应
            full_response = ""
            async for chunk in self.chat.stream_chat_with_interrupt(prompt):
                full_response += chunk
                print(f"📝 收到chunk: '{chunk}'")

                # 检测JSON代码块开始
                if "```json" in full_response and not json_started:
                    json_started = True
                    # 提取TTS内容（JSON代码块之前的内容）
                    json_start_pos = full_response.find("```json")
                    tts_content = full_response[:json_start_pos].strip()

                    # 处理剩余的句子缓冲区
                    if sentence_buffer.strip() and tts_streaming_active:
                        print(f"🎵 处理最后的句子缓冲区: {sentence_buffer}")
                        await self.send_real_time_tts(sentence_buffer.strip(), conv_id)
                        sentence_buffer = ""

                    # 停止TTS流式播放
                    if tts_streaming_active and not tts_content_finalized:
                        print(f"🛑 检测到JSON标记，完成TTS流式播放")
                        tts_streaming_active = False
                        tts_content_finalized = True
                        print(f"📝 TTS内容已通过实时流式播放完成: {tts_content}")

                # 如果还没开始JSON块，进行实时TTS处理
                elif not json_started:
                    # 检查当前chunk是否包含```
                    if "```" in chunk:
                        # 找到```的位置，只处理之前的部分
                        backtick_pos = chunk.find("```")
                        if backtick_pos > 0:
                            clean_chunk = chunk[:backtick_pos]
                            if clean_chunk.strip():
                                sentence_buffer += clean_chunk
                                # 处理最后的内容
                                if sentence_buffer.strip():
                                    await self.send_real_time_tts(sentence_buffer.strip(), conv_id)
                                    sentence_buffer = ""

                        # 停止流式播放
                        if tts_streaming_active and not tts_content_finalized:
                            print(f"🛑 检测到```标记，完成TTS流式播放")
                            tts_streaming_active = False
                            tts_content_finalized = True

                        # 跳过这个chunk的剩余部分
                        continue

                    # 开始流式TTS播放
                    if not tts_streaming_active:
                        print(f"🎵 开始实时流式TTS播放")
                        tts_streaming_active = True

                    # 将chunk添加到句子缓冲区
                    sentence_buffer += chunk

                    # 智能分句并实时发送TTS
                    sentences = await self.extract_complete_sentences(sentence_buffer)
                    for sentence in sentences:
                        if sentence.strip():
                            print(f"🎵 实时发送TTS句子: {sentence}")
                            await self.send_real_time_tts(sentence, conv_id)

                    # 更新缓冲区（移除已处理的句子）
                    if sentences:
                        # 计算已处理的字符数
                        processed_chars = sum(len(s) for s in sentences)
                        sentence_buffer = sentence_buffer[processed_chars:]

            print(f"🧠 流式处理完成，原始回复: {full_response}")

            # 处理剩余的句子缓冲区
            if sentence_buffer.strip() and tts_streaming_active:
                print(f"🎵 处理最终剩余的句子缓冲区: {sentence_buffer}")
                await self.send_real_time_tts(sentence_buffer.strip(), conv_id)

            # 解析JSON部分
            try:
                # 提取JSON内容
                json_start = full_response.find("```json") + 7
                json_end = full_response.find("```", json_start)

                if json_start > 6 and json_end > json_start:
                    json_str = full_response[json_start:json_end].strip()
                    compact_result = json.loads(json_str)

                    print(f"✅ JSON解析成功: {compact_result}")
                    print(f"🔧 模拟执行函数调用: {compact_result.get('call', {})}")

                else:
                    raise ValueError("未找到有效的JSON格式")

            except Exception as parse_error:
                print(f"⚠️ JSON解析失败: {parse_error}")

        except Exception as e:
            print(f"❌ 流式处理出错: {e}")

async def test_realtime_tts():
    """测试实时TTS功能"""
    print("🚀 开始测试实时TTS功能")
    
    handler = RealTimeTTSHandler()
    conv_id = "test_conv_123"
    
    # 模拟用户输入
    transcript = "我想买一个手机"
    prompt = f"用户说: {transcript}，请回复并提供搜索建议"
    
    print(f"👤 用户输入: {transcript}")
    print("🤖 开始AI回复...")
    
    # 测试流式TTS处理
    await handler.stream_process_with_immediate_tts(prompt, transcript, conv_id)
    
    print("\n✅ 实时TTS测试完成!")

if __name__ == "__main__":
    asyncio.run(test_realtime_tts())
