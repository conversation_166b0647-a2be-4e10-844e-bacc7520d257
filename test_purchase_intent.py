#!/usr/bin/env python3
"""
测试购买意图识别的脚本
"""

import asyncio
import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import VoiceSearchAgent

async def test_purchase_intent():
    """测试购买意图识别"""
    
    # 创建代理实例
    agent = VoiceSearchAgent()
    
    # 测试用例
    test_cases = [
        "我想买小米手机",
        "那就买这个呗", 
        "需要",
        "买个苹果手机",
        "要华为电脑",
        "找找耳机",
        "搜索平板电脑",
        "你好",  # 非购买意图
        "天气怎么样",  # 信息查询
    ]
    
    print("🧪 开始测试购买意图识别...")
    
    for i, test_input in enumerate(test_cases):
        print(f"\n{'='*50}")
        print(f"测试 {i+1}: '{test_input}'")
        print(f"{'='*50}")
        
        try:
            # 测试备用意图检测
            backup_result = await agent.backup_intent_detection(test_input)
            print(f"🔄 备用意图检测结果: {backup_result}")
            
            # 分析结果
            if backup_result:
                intent = backup_result.get('intent', 'unknown')
                call_info = backup_result.get('call', {})
                function_name = call_info.get('name', 'null')
                
                print(f"📊 分析结果:")
                print(f"   意图: {intent}")
                print(f"   函数调用: {function_name}")
                if function_name == 'search_products':
                    query = call_info.get('query', '')
                    print(f"   搜索词: '{query}'")
                    print(f"   ✅ 成功识别为购买意图！")
                elif function_name == 'web_search':
                    query = call_info.get('query', '')
                    print(f"   查询词: '{query}'")
                    print(f"   ℹ️ 识别为信息查询")
                else:
                    print(f"   💬 识别为普通交互")
            else:
                print(f"   ❌ 备用检测失败")
                
        except Exception as e:
            print(f"❌ 测试出错: {e}")
            import traceback
            traceback.print_exc()
    
    print(f"\n{'='*50}")
    print("🧪 测试完成！")
    print(f"{'='*50}")

if __name__ == "__main__":
    asyncio.run(test_purchase_intent())
