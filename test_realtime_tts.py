#!/usr/bin/env python3
"""
测试实时TTS功能的脚本 - 简化版本
"""

import asyncio
from typing import List

async def extract_complete_sentences(text_buffer: str) -> List[str]:
    """从文本缓冲区中提取完整的句子"""
    try:
        # 中文句子结束标点符号
        sentence_endings = ['。', '！', '？', '；', '.', '!', '?', ';', '…', '...']

        sentences = []
        current_sentence = ""

        i = 0
        while i < len(text_buffer):
            char = text_buffer[i]
            current_sentence += char

            # 检查是否是句子结束符
            if char in sentence_endings:
                # 检查是否是省略号的一部分
                if char == '.' and i + 2 < len(text_buffer) and text_buffer[i:i+3] == '...':
                    current_sentence += text_buffer[i+1:i+3]
                    i += 2

                # 添加完整句子
                if current_sentence.strip():
                    sentences.append(current_sentence.strip())
                    current_sentence = ""

            i += 1

        # 如果缓冲区中有足够长的未完成句子（超过10个字符），也发送出去
        if len(current_sentence.strip()) > 10:
            # 寻找合适的断点（逗号、顿号等）
            break_points = ['，', '、', ',', ' ']
            best_break = -1

            for j in range(len(current_sentence) - 1, max(0, len(current_sentence) - 5), -1):
                if current_sentence[j] in break_points:
                    best_break = j + 1
                    break

            if best_break > 0:
                sentences.append(current_sentence[:best_break].strip())

        return sentences

    except Exception as e:
        print(f"❌ 提取句子出错: {e}")
        return []

async def simulate_realtime_tts(text: str):
    """模拟实时TTS发送"""
    print(f"🎵 实时TTS发送: '{text}'")
    # 这里模拟TTS处理时间
    await asyncio.sleep(0.05)
    print(f"✅ TTS播放完成: '{text}'")

async def test_sentence_extraction():
    """测试句子提取功能"""
    print("🔍 测试句子提取功能:")

    test_cases = [
        "你好！我想买手机。",
        "价格在3000元以下，有什么推荐？",
        "这是一个很长的句子，包含逗号，顿号、以及其他标点符号。",
        "短句。另一个句子！还有问号？",
        "这是一个没有结束标点的长句子，应该在合适的地方断开"
    ]

    for test_text in test_cases:
        sentences = await extract_complete_sentences(test_text)
        print(f"输入: {test_text}")
        print(f"输出: {sentences}")
        print("---")

async def test_streaming_simulation():
    """模拟流式文本处理"""
    print("\n🎵 模拟流式文本处理:")

    # 模拟LLM逐字符生成的文本
    full_text = "你好！我正在为您搜索相关商品。请稍等片刻，我会为您找到最合适的选择。"

    sentence_buffer = ""

    for char in full_text:
        sentence_buffer += char

        # 每次添加字符后检查是否有完整句子
        sentences = await extract_complete_sentences(sentence_buffer)

        for sentence in sentences:
            if sentence.strip():
                await simulate_realtime_tts(sentence)
                # 从缓冲区移除已处理的句子
                sentence_buffer = sentence_buffer[len(sentence):].lstrip()

        # 模拟字符生成间隔
        await asyncio.sleep(0.02)

    # 处理剩余内容
    if sentence_buffer.strip():
        await simulate_realtime_tts(sentence_buffer.strip())

if __name__ == "__main__":
    print("🚀 启动实时TTS测试")

    # 运行测试
    asyncio.run(test_sentence_extraction())
    asyncio.run(test_streaming_simulation())

    print("\n🎉 所有测试完成!")
