#!/usr/bin/env python3
"""
测试TTS打断逻辑的脚本
验证优化后的TTS队列清空和优先级播放功能
"""

import asyncio
import sys
from pathlib import Path

# 添加项目路径
sys.path.insert(0, str(Path(__file__).parent))
sys.path.insert(0, str(Path(__file__).parent / "thirdparty" / "realtime_chat_v1"))

async def test_tts_interrupt_logic():
    """测试TTS打断逻辑"""
    print("🧪 开始测试TTS打断逻辑...")
    
    try:
        # 模拟队列操作
        test_queue = asyncio.Queue()
        
        # 添加一些测试内容到队列
        await test_queue.put("第一条TTS内容")
        await test_queue.put("第二条TTS内容")
        await test_queue.put("第三条TTS内容")
        
        print(f"📝 队列初始大小: {test_queue.qsize()}")
        
        # 模拟清空队列的操作
        async def clear_queue(queue):
            """异步清空队列的函数"""
            cleared_count = 0
            while not queue.empty():
                try:
                    item = await queue.get()
                    queue.task_done()
                    cleared_count += 1
                    print(f"🗑️ 清空项目: {item}")
                except asyncio.QueueEmpty:
                    break
            return cleared_count
        
        # 执行清空操作
        cleared = await clear_queue(test_queue)
        print(f"✅ 清空了 {cleared} 个项目")
        print(f"📝 队列清空后大小: {test_queue.qsize()}")
        
        # 添加高优先级内容
        await test_queue.put("高优先级TTS内容")
        print(f"🔊 添加高优先级内容后队列大小: {test_queue.qsize()}")
        
        # 验证队列内容
        if not test_queue.empty():
            item = await test_queue.get()
            print(f"✅ 获取到的内容: {item}")
        
        print("🎉 TTS打断逻辑测试完成！")
        
    except Exception as e:
        print(f"❌ 测试出错: {e}")
        import traceback
        traceback.print_exc()

async def test_priority_logic():
    """测试优先级逻辑"""
    print("\n🧪 开始测试优先级逻辑...")
    
    try:
        # 模拟优先级TTS发送
        class MockTTSEvent:
            def __init__(self, content, priority=False):
                self.content = content
                self.priority = priority
                self.id = "test_id"
                self.type = 'tts'
        
        # 创建测试队列
        tts_queue = asyncio.Queue()
        
        # 添加普通TTS内容
        normal_events = [
            MockTTSEvent("普通内容1"),
            MockTTSEvent("普通内容2"),
            MockTTSEvent("普通内容3")
        ]
        
        for event in normal_events:
            await tts_queue.put(event)
        
        print(f"📝 添加普通内容后队列大小: {tts_queue.qsize()}")
        
        # 模拟高优先级内容到达，需要清空队列
        print("🔊 模拟高优先级内容到达，清空队列...")
        
        # 清空队列
        cleared_count = 0
        while not tts_queue.empty():
            try:
                item = await tts_queue.get()
                cleared_count += 1
                print(f"🗑️ 清空: {item.content}")
            except asyncio.QueueEmpty:
                break
        
        print(f"✅ 清空了 {cleared_count} 个普通内容")
        
        # 添加高优先级内容
        priority_event = MockTTSEvent("高优先级内容", priority=True)
        await tts_queue.put(priority_event)
        
        print(f"🔊 添加高优先级内容后队列大小: {tts_queue.qsize()}")
        
        # 验证高优先级内容能立即播放
        if not tts_queue.empty():
            item = await tts_queue.get()
            print(f"✅ 立即播放的内容: {item.content} (优先级: {item.priority})")
        
        print("🎉 优先级逻辑测试完成！")
        
    except Exception as e:
        print(f"❌ 优先级测试出错: {e}")
        import traceback
        traceback.print_exc()

async def test_conversation_interrupt():
    """测试新对话打断逻辑"""
    print("\n🧪 开始测试新对话打断逻辑...")

    try:
        # 模拟TTS队列中有内容
        tts_queue = asyncio.Queue()

        # 添加旧对话的TTS内容
        old_contents = [
            "这是第一轮对话的内容",
            "正在为您搜索商品",
            "找到了很多相关商品"
        ]

        for content in old_contents:
            await tts_queue.put(content)

        print(f"📝 旧对话TTS队列大小: {tts_queue.qsize()}")

        # 模拟新对话开始 - 强制清空队列
        print("🎤 新对话开始，强制清空TTS队列...")

        # 清空队列
        cleared_count = 0
        while not tts_queue.empty():
            try:
                item = await tts_queue.get()
                cleared_count += 1
                print(f"🗑️ 清空旧内容: {item}")
            except asyncio.QueueEmpty:
                break

        print(f"✅ 清空了 {cleared_count} 个旧TTS内容")
        print(f"📝 清空后队列大小: {tts_queue.qsize()}")

        # 添加新对话内容
        new_content = "这是新对话的立即响应"
        await tts_queue.put(new_content)

        print(f"🔊 添加新对话内容后队列大小: {tts_queue.qsize()}")

        # 验证新内容能立即播放
        if not tts_queue.empty():
            item = await tts_queue.get()
            print(f"✅ 立即播放新内容: {item}")

        print("🎉 新对话打断逻辑测试完成！")

    except Exception as e:
        print(f"❌ 新对话打断测试出错: {e}")
        import traceback
        traceback.print_exc()

async def test_audio_queue_clearing():
    """测试音频队列清空逻辑"""
    print("\n🧪 开始测试音频队列清空逻辑...")

    try:
        # 模拟voice_queue中有混合内容
        voice_queue = asyncio.Queue()

        # 添加音频数据（tuple类型）
        import numpy as np
        audio_data1 = (16000, np.array([[1, 2, 3, 4, 5]]))
        audio_data2 = (16000, np.array([[6, 7, 8, 9, 10]]))
        audio_data3 = (16000, np.array([[11, 12, 13, 14, 15]]))

        # 添加非音频事件
        class MockChatEvent:
            def __init__(self, content):
                self.content = content
                self.type = 'msg'

        text_event1 = MockChatEvent("这是文本消息1")
        text_event2 = MockChatEvent("这是文本消息2")

        # 混合添加到队列
        await voice_queue.put(audio_data1)
        await voice_queue.put(text_event1)
        await voice_queue.put(audio_data2)
        await voice_queue.put(text_event2)
        await voice_queue.put(audio_data3)

        print(f"📝 混合队列初始大小: {voice_queue.qsize()}")

        # 模拟清空音频数据，保留文本事件
        print("🗑️ 开始清空音频数据...")

        cleared_audio_count = 0
        preserved_events = []

        # 临时存储所有项目
        temp_items = []
        while not voice_queue.empty():
            try:
                item = voice_queue.get_nowait()
                temp_items.append(item)
            except asyncio.QueueEmpty:
                break

        # 分类处理
        for item in temp_items:
            if isinstance(item, tuple):
                # 音频数据，丢弃
                cleared_audio_count += 1
                print(f"🗑️ 清空音频数据: sample_rate={item[0]}, shape={item[1].shape}")
            else:
                # 非音频数据，保留
                preserved_events.append(item)
                await voice_queue.put(item)

        print(f"✅ 清空了 {cleared_audio_count} 个音频数据")
        print(f"✅ 保留了 {len(preserved_events)} 个文本事件")
        print(f"📝 清空后队列大小: {voice_queue.qsize()}")

        # 验证保留的内容
        print("📋 验证保留的内容:")
        while not voice_queue.empty():
            item = voice_queue.get_nowait()
            print(f"  - 保留项目: {item.content}")

        print("🎉 音频队列清空逻辑测试完成！")

    except Exception as e:
        print(f"❌ 音频队列清空测试出错: {e}")
        import traceback
        traceback.print_exc()

async def test_app2_bitts_pattern():
    """测试按照app2_bitts.py模式的打断逻辑"""
    print("\n🧪 开始测试app2_bitts.py模式的打断逻辑...")

    try:
        # 模拟voice_queue和voice_inqueue
        voice_queue = asyncio.Queue()
        voice_inqueue = asyncio.Queue()

        # 添加一些内容到队列
        await voice_queue.put(("audio_data_1", "sample_rate_1"))
        await voice_queue.put(("audio_data_2", "sample_rate_2"))
        await voice_inqueue.put("tts_text_1")
        await voice_inqueue.put("tts_text_2")

        print(f"📝 初始状态 - voice_queue: {voice_queue.qsize()}, voice_inqueue: {voice_inqueue.qsize()}")

        # 模拟app2_bitts.py的打断顺序
        print("🔊 执行app2_bitts.py模式的打断...")

        # 1. 先清空voice_queue（输出队列）
        async def clear_queue(queue):
            cleared_count = 0
            while not queue.empty():
                try:
                    item = await queue.get()
                    queue.task_done()
                    cleared_count += 1
                    print(f"🗑️ 清空: {item}")
                except asyncio.QueueEmpty:
                    break
            return cleared_count

        cleared_output = await clear_queue(voice_queue)
        print(f"✅ 清空voice_queue: {cleared_output} 个项目")

        # 2. 模拟发送打断事件
        class MockInterruptEvent:
            def __init__(self):
                self.type = "[interruption]"
                self.content = "[Conversation interrupted]"

        interrupt_event = MockInterruptEvent()
        await voice_queue.put(interrupt_event)
        print("📤 发送打断事件")

        # 3. 模拟TTS打断（这里只是打印）
        print("🔊 执行TTS打断")

        # 4. 清空输入队列
        cleared_input = await clear_queue(voice_inqueue)
        print(f"✅ 清空voice_inqueue: {cleared_input} 个项目")

        # 5. 再次发送打断事件
        await voice_queue.put(interrupt_event)
        print("📤 再次发送打断事件")

        print(f"📝 最终状态 - voice_queue: {voice_queue.qsize()}, voice_inqueue: {voice_inqueue.qsize()}")

        # 验证队列内容
        print("📋 验证voice_queue内容:")
        while not voice_queue.empty():
            item = voice_queue.get_nowait()
            if hasattr(item, 'type'):
                print(f"  - 事件: {item.type}")
            else:
                print(f"  - 数据: {item}")

        print("🎉 app2_bitts.py模式测试完成！")

    except Exception as e:
        print(f"❌ app2_bitts.py模式测试出错: {e}")
        import traceback
        traceback.print_exc()

async def main():
    """主测试函数"""
    print("🚀 开始TTS打断优化测试 (app2_bitts.py模式)")
    print("=" * 60)

    await test_tts_interrupt_logic()
    await test_priority_logic()
    await test_conversation_interrupt()
    await test_audio_queue_clearing()
    await test_app2_bitts_pattern()

    print("\n" + "=" * 60)
    print("✅ 所有测试完成！")
    print("\n📋 app2_bitts.py模式修复总结:")
    print("1. ✅ 按照app2_bitts.py的成功顺序执行打断")
    print("2. ✅ 先清空voice_queue（输出队列）")
    print("3. ✅ 发送打断事件到前端")
    print("4. ✅ 执行TTS打断")
    print("5. ✅ 清空voice_inqueue（输入队列）")
    print("6. ✅ 再次发送打断事件确保前端收到")
    print("7. ✅ 用户说话时也使用相同的打断模式")
    print("\n🎯 预期效果:")
    print("- 按照成功的app2_bitts.py模式执行打断")
    print("- 确保输出队列和输入队列都被清空")
    print("- TTS打断更加彻底和可靠")
    print("- 新响应能立即开始播放")

if __name__ == "__main__":
    asyncio.run(main())
